import re
import json
import uuid
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional
from concurrent.futures import ProcessPoolExecutor, as_completed
from bs4 import BeautifulSoup

class CareCreditExtractor:
    def __init__(self):
        self.data = {}

    def extract_profile(self, html_content: str) -> Dict[str, Any]:
        if not html_content or not html_content.strip():
            raise ValueError("HTML content cannot be empty")
        
        soup = BeautifulSoup(html_content, 'html.parser')
        dl_details = soup.find(id='dl-details')
        
        if not dl_details:
            raise ValueError("dl-details section not found in HTML")
        
        data = self._initialize_data()
        self._extract_basic_info(dl_details, data)
        self._extract_contact_info(dl_details, data)
        self._extract_specialties_and_dentists(dl_details, data)
        self._clean_data(data)
        
        return data

    def _initialize_data(self) -> Dict[str, Any]:
        return {
            "id": str(uuid.uuid4()),
            "name": None,
            "category": None,
            "address": None,
            "phone": None,
            "specialties": None,
            "doctors": None,
            "created_at": datetime.now().isoformat()
        }

    def _extract_basic_info(self, dl_details: BeautifulSoup, data: Dict[str, Any]):
        h1_tag = dl_details.find('h1')
        if h1_tag:
            data['name'] = h1_tag.get_text(strip=True)
        
        h2_tag = dl_details.find('h2')
        if h2_tag:
            data['category'] = h2_tag.get_text(strip=True)

    def _extract_contact_info(self, dl_details: BeautifulSoup, data: Dict[str, Any]):
        address_div = dl_details.find('div', class_='dl-detail-pin')
        if address_div:
            address_link = address_div.find('a')
            if address_link:
                data['address'] = self._clean_address(address_link.get_text(strip=True))
        
        phone_div = dl_details.find('div', class_='dl-detail-phone')
        if phone_div:
            data['phone'] = self._clean_phone(phone_div.get_text(strip=True))

    def _extract_specialties_and_dentists(self, dl_details: BeautifulSoup, data: Dict[str, Any]):
        detail_items = dl_details.find_all('div', class_='dl-detail-item')
        
        for item in detail_items:
            strong_tag = item.find('strong')
            if strong_tag:
                label = strong_tag.get_text(strip=True).lower()
                
                if 'specialties' in label:
                    content = item.get_text(strip=True)
                    content = content.replace(strong_tag.get_text(strip=True), '').strip()
                    data['specialties'] = content
                
                elif 'doctor' in label or 'dentist' in label:
                    content = item.get_text(strip=True)
                    content = content.replace(strong_tag.get_text(strip=True), '').strip()
                    data['doctors'] = content

    def _clean_address(self, address: str) -> str:
        if not address:
            return None
        return re.sub(r'\s+', ' ', address.strip())

    def _clean_phone(self, phone: str) -> str:
        if not phone:
            return None
        cleaned = re.sub(r'[^\d\-\(\)\s\+]', '', phone.strip())
        return cleaned if cleaned else None

    def _clean_data(self, data: Dict[str, Any]):
        for key, value in data.items():
            if isinstance(value, str) and value:
                data[key] = value.strip()
            elif not value:
                data[key] = None

def process_single_file(file_path: Path) -> Optional[Dict[str, Any]]:
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        extractor = CareCreditExtractor()
        business_data = extractor.extract_profile(html_content)
        business_data['source_file'] = file_path.name
        return business_data
    except Exception as e:
        print(f"Error processing file {file_path.name}: {e}")
        return None

def process_directory_in_parallel(input_directory: str, output_file: str):
    input_path = Path(input_directory)
    if not input_path.exists():
        raise FileNotFoundError(f"Input directory does not exist: {input_directory}")
    
    html_files = list(input_path.glob("*.html"))
    if not html_files:
        print("No HTML files found in the directory.")
        return
    
    print(f"Found {len(html_files)} HTML files to process...")
    all_business_data = []
    
    with ProcessPoolExecutor() as executor:
        future_to_file = {executor.submit(process_single_file, file): file for file in html_files}
        
        print("Processing files in parallel...")
        for i, future in enumerate(as_completed(future_to_file)):
            file_path = future_to_file[future]
            try:
                result = future.result()
                if result:
                    all_business_data.append(result)
                print(f"  Processed {i + 1}/{len(html_files)}: {file_path.name}", end='\r')
            except Exception as exc:
                print(f"\n{file_path.name} generated an exception: {exc}")
    
    print(f"\nSuccessfully processed {len(all_business_data)} files.")
    
    if all_business_data:
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(all_business_data, f, indent=2, ensure_ascii=False)
        
        print(f"Results saved to: {output_file}")
        print(f"Total records extracted: {len(all_business_data)}")
    else:
        print("No data was extracted from any files.")

def main():
    input_directory = r"C:\Users\<USER>\OneDrive\Desktop\SUMMER_INTERSHIP\leads-generator\scrapingbee\carecredit_pages\new-york-ny"
    output_file = "extracted_data.json"
    
    try:
        process_directory_in_parallel(input_directory, output_file)
    except FileNotFoundError as e:
        print(f"Error: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    main()

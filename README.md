# 🦷 Leads Generator

A unified solution to generate, link, and manage dental practice leads from multiple sources.

---

## 🚀 Key Features

- **Practice Linking & Master Record Creation**  
  Maps and unifies practices from all sources.  
  Assigns a unique UUID to each distinct practice using normalized matching keys.

- **Source-Agnostic Normalization**  
  Consistent data cleaning and standardization across all sources.

---

## 🛠️ Getting Started

| Step | What to Do                                   | Where to Look                |
|------|----------------------------------------------|------------------------------|
| 1    | Process NPI data                             | `NPIs/sample_usage.py`       |
| 2    | Extract BBB data                             | `bbb-leads-extractor/`       |
| 3    | Unify/link data into master records          | `mapBBBtoNPI.py`             |

---

## 📦 Data Sources

- NPI Registry API (healthcare provider info)
- Better Business Bureau business profiles
- CareCredit business profiles
- **Easily add more sources** using the same normalization and linking logic

---

## 🧩 Naming Conventions

- **Folders:** `numberofdonuts` / `NumberOfDonuts`
- **Files:** `numberOfDonuts.py`
- **Variables/Functions:** `number_of_donuts`

---

## 💡 Why This Approach?

- 🟢 **Consistent, deduplicated master records**
- 🟢 **Effortlessly scalable**—add new sources without changing core logic
- 🟢 **Reliable mapping** using flexible, source-agnostic

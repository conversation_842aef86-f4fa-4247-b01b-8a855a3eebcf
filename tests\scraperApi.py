import requests
payload = {'api_key': '9a7e9c328ac12d7583c7dba0e450ad95', 'url': 'https://www.yelp.com/biz/circle-32-dental-austin'}
r = requests.get('https://api.scraperapi.com', params=payload)
print(r.text)
# Scrapy users can simply replace the urls in their start_urls and parse function
# ...other scrapy setup code
url = 'https://www.yelp.com/biz/circle-32-dental-austin'
start_urls = ['http://api.scraperapi.com?api_key=APIKEY&url=' + url]

def parse(response):
    # Save the whole HTML page to a file
    filename = "output.html"
    with open(filename, "w", encoding="utf-8") as f:
        f.write(response)
    print(f"Saved file {filename}")

parse(r.text)
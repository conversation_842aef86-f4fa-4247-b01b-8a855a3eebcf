import os
import time
import async<PERSON>
from pathlib import Path
from scrapingbee import ScrapingBeeClient
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv
from datetime import datetime
import threading

load_dotenv()

API_KEY = os.getenv('SCRAPING_BEE_API_KEY')
MAX_CONCURRENT_REQUESTS = 10
OUTPUT_DIR = Path("carecredit_pages")
LOG_FILE = "f_logs.txt"
FAILED_URLS_FILE = "failed_urls.txt"
SUCCESS_URLS_FILE = "success_urls.txt"

# Create output directory if it doesn't exist
OUTPUT_DIR.mkdir(exist_ok=True)

# Thread-safe file writing
log_lock = threading.Lock()
failed_lock = threading.Lock()
success_lock = threading.Lock()

def log_message(message):
    """Thread-safe logging to both console and file"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] {message}"
    
    # Print to console
    print(message)
    
    # Write to log file
    with log_lock:
        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')

def log_failed_url(url, error):
    """Thread-safe logging of failed URLs in real-time"""
    with failed_lock:
        with open(FAILED_URLS_FILE, 'a', encoding='utf-8') as f:
            f.write(f"{url}\n")

def log_success_url(url):
    """Thread-safe logging of successful URLs in real-time"""
    with success_lock:
        with open(SUCCESS_URLS_FILE, 'a', encoding='utf-8') as f:
            f.write(f"{url}\n")

def initialize_log_files():
    """Clear/initialize log files at start"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Clear and initialize log file
    with open(LOG_FILE, 'w', encoding='utf-8') as f:
        f.write(f"[{timestamp}] Scraping session started\n")
    
    # Clear failed and success files
    open(FAILED_URLS_FILE, 'w').close()
    open(SUCCESS_URLS_FILE, 'w').close()

def load_urls():
    with open('remaining_urls.txt', 'r') as f:
        return [line.strip() for line in f if line.strip()]

def get_safe_filename(url):
    url = url.rstrip('/')
    parts = url.split('/')
    
    if len(parts) >= 2:
        location = parts[-2]      
        business = parts[-1]      
        location_dir = OUTPUT_DIR / location
        location_dir.mkdir(exist_ok=True)
        return f"{location}/{business}.html"
    
    return "unknown.html"

def sync_scrape(client, url):
    """Synchronous scrape function to be run in thread pool"""
    return client.get(url, params={'render_js': False})

async def scrape_url(client, url, semaphore, executor):
    async with semaphore:
        try:
            # Offload blocking I/O to thread pool
            resp = await asyncio.get_event_loop().run_in_executor(
                executor, sync_scrape, client, url
            )

            if resp.ok:
                filename = OUTPUT_DIR / get_safe_filename(url)
                filename.parent.mkdir(parents=True, exist_ok=True)
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(resp.text)
                
                # Log success in real-time
                log_message(f"✅ {url}")
                log_success_url(url)
                return True, url, None
            else:
                error_msg = f"HTTP {resp.status_code}"
                # Log failure in real-time
                log_message(f"❌ {url} → {error_msg}")
                log_failed_url(url, error_msg)
                return False, url, error_msg

        except Exception as e:
            error_msg = str(e)
            # Log failure in real-time
            log_message(f"❌ {url} → {error_msg}")
            log_failed_url(url, error_msg)
            return False, url, error_msg

async def main():
    # Initialize log files
    initialize_log_files()
    
    urls = load_urls()
    client = ScrapingBeeClient(api_key=API_KEY)
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
    executor = ThreadPoolExecutor(max_workers=MAX_CONCURRENT_REQUESTS)

    log_message(f"Starting scrape of {len(urls)} URLs with {MAX_CONCURRENT_REQUESTS} concurrent requests...")
    start_time = time.time()

    # Create tasks for all URLs
    tasks = [
        scrape_url(client, url, semaphore, executor)
        for url in urls
    ]

    # Process in batches to avoid memory issues with 83K URLs
    batch_size = 1000
    all_results = []
    successful_count = 0
    failed_count = 0
    
    for i in range(0, len(tasks), batch_size):
        batch = tasks[i:i + batch_size]
        batch_start = time.time()
        
        batch_num = i//batch_size + 1
        total_batches = (len(tasks) + batch_size - 1)//batch_size
        log_message(f"Processing batch {batch_num}/{total_batches}")
        
        batch_results = await asyncio.gather(*batch, return_exceptions=True)
        all_results.extend(batch_results)
        
        # Count results in this batch
        batch_success = 0
        batch_failed = 0
        for result in batch_results:
            if isinstance(result, Exception):
                batch_failed += 1
            else:
                success, url, error = result
                if success:
                    batch_success += 1
                else:
                    batch_failed += 1
        
        successful_count += batch_success
        failed_count += batch_failed
        
        batch_time = time.time() - batch_start
        log_message(f"Batch completed in {batch_time:.2f}s")
        
        # Small delay between batches
        await asyncio.sleep(0.1)

    total_time = time.time() - start_time
    
    log_message("=" * 60)
    log_message("SCRAPING COMPLETE")
    log_message("=" * 60)
    log_message(f"Total time: {total_time:.2f}s")
    log_message(f"Total URLs: {len(urls)}")
    log_message(f"Successful: {successful_count} ({successful_count/len(urls)*100:.1f}%)")
    log_message(f"Failed: {failed_count} ({failed_count/len(urls)*100:.1f}%)")
    log_message(f"Average time per URL: {total_time/len(urls):.3f}s")
    
    if failed_count > 0:
        log_message(f"\n{failed_count} failed URLs saved in {FAILED_URLS_FILE}")
        log_message(f"{successful_count} successful URLs saved in {SUCCESS_URLS_FILE}")
        log_message("To retry failed URLs: cp failed_urls.txt urls.txt && python script.py")
    
    # Cleanup
    executor.shutdown(wait=True)

if __name__ == "__main__":
    asyncio.run(main())
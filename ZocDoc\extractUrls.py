#!/usr/bin/env python3
import requests
import re
import time
from lxml import etree

# CONFIGURATION
BASE_URL       = "https://www.zocdoc.com/sitemaps/profiles-{}-sitemap.xml"
OUTPUT_FILE    = "dental_urls.txt"
START_INDEX    = 1
MAX_INDEX      = 100      # safety cap
REQUEST_DELAY  = 1.0      # seconds between requests
RETRY_DELAY    = 2.0      # wait before retrying a 403

# Primary headers (realistic browser UA + common headers)
BROWSER_HEADERS = {
    "User-Agent": ("Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                   "AppleWebKit/537.36 (KHTML, like Gecko) "
                   "Chrome/********* Safari/537.36"),
    "Accept": ("text/html,application/xhtml+xml,application/xml;"
               "q=0.9,image/avif,image/webp,*/*;q=0.8"),
    "Accept-Language": "en-US,en;q=0.5",
    "Accept-Encoding": "gzip, deflate, br",
    "Referer": "https://www.zocdoc.com/",
}

# Regex to match any dentistry-related pattern
DENTAL_REGEX = re.compile(
    r"/dentist/|"      # explicit dentist path
    r"dmd|dds|"        # degree abbreviations
    r"dental|orthodontist|oral-surgeon",
    re.IGNORECASE
)

def fetch_sitemap(idx):
    """Fetch sitemap XML. On 403, retry once after a delay; return bytes or None."""
    url = BASE_URL.format(idx)
    session = requests.Session()
    for attempt in range(2):
        r = session.get(url, headers=BROWSER_HEADERS, timeout=10)
        if r.status_code == 200:
            return r.content
        if r.status_code == 404:
            return None
        if r.status_code == 403 and attempt == 0:
            print(" (403 forbidden — retrying after delay)", end="", flush=True)
            time.sleep(RETRY_DELAY)
            continue
        r.raise_for_status()
    return None

def parse_urls(xml_bytes):
    ns = {"ns": "http://www.sitemaps.org/schemas/sitemap/0.9"}
    root = etree.fromstring(xml_bytes)
    return [loc.text for loc in root.xpath("//ns:loc", namespaces=ns)]

def is_dental_url(url):
    return bool(DENTAL_REGEX.search(url))

def main():
    all_dental_urls = set()
    print(f"Starting extraction – writing to '{OUTPUT_FILE}'")
    for idx in range(START_INDEX, START_INDEX + MAX_INDEX):
        print(f"  → Sitemap #{idx}...", end="", flush=True)
        try:
            xml = fetch_sitemap(idx)
        except Exception as e:
            print(f" error: {e}")
            continue

        if xml is None:
            print(" 404 or no content, stopping.")
            break

        urls = parse_urls(xml)
        dental = [u for u in urls if is_dental_url(u)]
        print(f" found {len(dental)}/{len(urls)} dental URLs.")
        all_dental_urls.update(dental)
        time.sleep(REQUEST_DELAY)

    # Write results
    with open(OUTPUT_FILE, "w") as fout:
        for url in sorted(all_dental_urls):
            fout.write(url + "\n")

    print(f"[✓] Done: {len(all_dental_urls)} unique dental URLs saved to '{OUTPUT_FILE}'.")

if __name__ == "__main__":
    main()

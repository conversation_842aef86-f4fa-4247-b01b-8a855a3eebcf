# Install the Python ScrapingBee library:    
# pip install scrapingbee

from scrapingbee import ScrapingBeeClient

client = ScrapingBeeClient(api_key='********************************************************************************')


#NOTE: This setting has 70% success rate
# response = client.get(
#   'https://www.yelp.com/biz/circle-32-dental-austin',
#   params={
#     'block_resources': False
#   }
# )

# print(response.text)


#NOTE: This setting has 97% success rate
response = client.get(
  'https://www.yelp.com',
  params={
    'js_scenario': {"instructions": [{"wait": 8000}, {"evaluate": "window.location.href='https://www.yelp.com/biz/circle-32-dental-austin'"}, {"wait": 5000}]},
    'block_resources': False
  }
)

print(response.text)

# save in html
with open('Bee_output.html', 'w', encoding='utf-8') as f:
    f.write(response.text)
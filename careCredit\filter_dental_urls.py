import json
import re

def filter_dental_urls(json_file_path, output_file_path):
    dental_patterns = [
        r'\bdental\b',
        r'\bdds\b',
        r'\bdmd\b',
        r'\bdent\b',
        r'\bdont\b',
        r'\bdontic\b',
        r'\boral\b',
        r'\bortho\b',
        r'\bimplant\b',
        r'\broot\b',
        r'\bbrace\b',
        r'\btooth\b',
        r'\bteeth\b',
        r'\bsmile\b'
        # dds | dent | dmd | dontic | oral | ortho | implant | root | brace | tooth | teeth | smile
    ]
    
    combined_pattern = '|'.join(dental_patterns)
    regex = re.compile(combined_pattern, re.IGNORECASE)
    
    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    dental_urls = []
    total_urls = len(data['sitemaps'])
    
    for entry in data['sitemaps']:
        url = entry['loc']
        if regex.search(url):
            dental_urls.append(url)
    
    with open(output_file_path, 'w', encoding='utf-8') as f:
        for url in dental_urls:
            f.write(url + '\n')
    
    print(f"Total URLs processed: {total_urls:,}")
    print(f"Dental URLs found: {len(dental_urls):,}")
    print(f"Percentage: {(len(dental_urls)/total_urls)*100:.1f}%")
    print(f"Dental URLs saved to: {output_file_path}")

if __name__ == "__main__":
    input_file = "urls/carecredit_doctor_urls.json"
    output_file = "azeem.txt"
    filter_dental_urls(input_file, output_file)
